// Codeunit for managing mass email operations
codeunit 50111 "Mass Email Management"
{
    procedure LoadContactsForSalesperson(SalespersonCode: Code[20]; var MassEmailContactBuffer: Record "Mass Email Contact Buffer")
    var
        Contact: Record Contact;
    begin
        MassEmailContactBuffer.Reset();
        MassEmailContactBuffer.DeleteAll();

        if SalespersonCode = '' then
            exit;

        Contact.Reset();
        Contact.SetRange("Salesperson Code", SalespersonCode);
        Contact.SetFilter("E-Mail", '<>%1', '');

        if Contact.FindSet() then
            repeat
                MassEmailContactBuffer.Init();
                MassEmailContactBuffer."Contact No." := Contact."No.";
                MassEmailContactBuffer."Contact Name" := Contact.Name;
                MassEmailContactBuffer."E-Mail" := Contact."E-Mail";
                MassEmailContactBuffer."Company Name" := Contact."Company Name";
                MassEmailContactBuffer."Salesperson Code" := Contact."Salesperson Code";
                MassEmailContactBuffer.Selected := true; // Default to selected
                MassEmailContactBuffer.Insert();
            until Contact.Next() = 0;
    end;

    procedure GetUserEmail(): Text[80]
    var
        UserSetup: Record "User Setup";
    begin
        // Get email from User Setup
        if UserSetup.Get(UserId) then
            if UserSetup."E-Mail" <> '' then
                exit(UserSetup."E-Mail");

        // Return empty if no email found
        exit('');
    end;

    procedure SendMassEmail(var MassEmailContactBuffer: Record "Mass Email Contact Buffer"; Subject: Text[250]; MessageBody: Text; var AttachmentInStream: InStream; AttachmentName: Text; AttachmentMimeType: Text): Boolean
    var
        EmailMessage: Codeunit "Email Message";
        Email: Codeunit Email;
        BCCRecipients: Text;
        FromEmail: Text[80];
        TempMassEmailContactBuffer: Record "Mass Email Contact Buffer" temporary;
    begin
        FromEmail := GetUserEmail();
        if FromEmail = '' then begin
            Message('No email address found for current user. Please configure your email in User Setup.');
            exit(false);
        end;

        // Build BCC recipients list from selected contacts
        BCCRecipients := '';
        TempMassEmailContactBuffer.Copy(MassEmailContactBuffer, true);
        TempMassEmailContactBuffer.Reset();
        TempMassEmailContactBuffer.SetRange(Selected, true);

        if not TempMassEmailContactBuffer.FindSet() then begin
            Message('No contacts selected for email.');
            exit(false);
        end;

        repeat
            if BCCRecipients <> '' then
                BCCRecipients += ';';
            BCCRecipients += TempMassEmailContactBuffer."E-Mail";
        until TempMassEmailContactBuffer.Next() = 0;

        // Create email message with BCC recipients as TO field (since BC doesn't support pure BCC)
        // Note: This will send to all recipients as TO, but they won't see each other's addresses
        EmailMessage.Create(BCCRecipients, Subject, MessageBody, true);

        // Add attachment if provided
        if AttachmentName <> '' then
            EmailMessage.AddAttachment(AttachmentName, AttachmentMimeType, AttachmentInStream);

        // Send the email
        if Email.Send(EmailMessage) then begin
            Message('Mass email sent successfully to %1 recipients.', TempMassEmailContactBuffer.Count());
            exit(true);
        end else begin
            Message('Failed to send mass email. Please check your email configuration.');
            exit(false);
        end;
    end;

    procedure GetSelectedContactCount(var MassEmailContactBuffer: Record "Mass Email Contact Buffer"): Integer
    var
        TempMassEmailContactBuffer: Record "Mass Email Contact Buffer" temporary;
    begin
        TempMassEmailContactBuffer.Copy(MassEmailContactBuffer, true);
        TempMassEmailContactBuffer.Reset();
        TempMassEmailContactBuffer.SetRange(Selected, true);
        exit(TempMassEmailContactBuffer.Count());
    end;
}
