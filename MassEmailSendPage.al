// Main page for mass email composition and sending
page 50111 "Mass Email Send"
{
    PageType = Card;
    ApplicationArea = All;
    UsageCategory = Tasks;
    Caption = 'Mass Email Send';
    SourceTable = "Mass Email Contact Buffer";
    SourceTableTemporary = true;

    layout
    {
        area(Content)
        {
            group(EmailComposition)
            {
                Caption = 'Email Composition';

                field(SalespersonCode; SalespersonCode)
                {
                    ApplicationArea = All;
                    Caption = 'Salesperson';
                    ToolTip = 'Select the salesperson whose contacts will receive the email.';
                    TableRelation = "Salesperson/Purchaser";

                    trigger OnValidate()
                    var
                        MassEmailMgt: Codeunit "Mass Email Management";
                    begin
                        MassEmailMgt.LoadContactsForSalesperson(SalespersonCode, Rec);
                        UpdateContactCount();
                        CurrPage.Update(false);
                    end;
                }

                field(FromEmail; FromEmail)
                {
                    ApplicationArea = All;
                    Caption = 'From';
                    ToolTip = 'Email will be sent from this address (your default email).';
                    Editable = false;
                }

                field(EmailInfo; 'Note: Emails will be sent to all selected contacts. Recipients will not see each other''s email addresses.')
                {
                    ApplicationArea = All;
                    Caption = '';
                    ToolTip = 'Information about how the mass email will be sent.';
                    Editable = false;
                    Style = Attention;
                    ShowCaption = false;
                }

                field(EmailSubject; EmailSubject)
                {
                    ApplicationArea = All;
                    Caption = 'Subject';
                    ToolTip = 'Enter the email subject.';
                }

                field(EmailBody; EmailBody)
                {
                    ApplicationArea = All;
                    Caption = 'Message';
                    ToolTip = 'Enter the email message body.';
                    MultiLine = true;
                }

                field(AttachmentFileName; AttachmentFileName)
                {
                    ApplicationArea = All;
                    Caption = 'Attachment';
                    ToolTip = 'Shows the name of the attached file.';
                    Editable = false;
                }
            }

            group(ContactSelection)
            {
                Caption = 'Contact Selection';

                field(SelectedContactsInfo; SelectedContactsInfo)
                {
                    ApplicationArea = All;
                    Caption = 'Selected Contacts';
                    ToolTip = 'Shows the number of selected contacts.';
                    Editable = false;
                    Style = Strong;
                }

                repeater(Contacts)
                {
                    field(Selected; Rec.Selected)
                    {
                        ApplicationArea = All;
                        ToolTip = 'Select/deselect this contact for the mass email.';

                        trigger OnValidate()
                        begin
                            UpdateContactCount();
                        end;
                    }
                    field("Contact No."; Rec."Contact No.")
                    {
                        ApplicationArea = All;
                        ToolTip = 'Contact number.';
                        Editable = false;
                    }
                    field("Contact Name"; Rec."Contact Name")
                    {
                        ApplicationArea = All;
                        ToolTip = 'Contact name.';
                        Editable = false;
                    }
                    field("E-Mail"; Rec."E-Mail")
                    {
                        ApplicationArea = All;
                        ToolTip = 'Contact email address.';
                        Editable = false;
                    }
                    field("Company Name"; Rec."Company Name")
                    {
                        ApplicationArea = All;
                        ToolTip = 'Company name.';
                        Editable = false;
                    }
                }
            }
        }
    }

    actions
    {
        area(Processing)
        {
            action(SelectAll)
            {
                ApplicationArea = All;
                Caption = 'Select All';
                ToolTip = 'Select all contacts for the mass email.';
                Image = SelectEntries;
                Promoted = true;
                PromotedCategory = Process;

                trigger OnAction()
                begin
                    SelectAllContacts(true);
                end;
            }

            action(DeselectAll)
            {
                ApplicationArea = All;
                Caption = 'Deselect All';
                ToolTip = 'Deselect all contacts.';
                Image = ClearFilter;
                Promoted = true;
                PromotedCategory = Process;

                trigger OnAction()
                begin
                    SelectAllContacts(false);
                end;
            }

            action(AttachFile)
            {
                ApplicationArea = All;
                Caption = 'Add Attachment';
                ToolTip = 'Add a file attachment to the email.';
                Image = Attach;
                Promoted = true;
                PromotedCategory = Process;

                trigger OnAction()
                begin
                    AttachFileToEmail();
                end;
            }

            action(DetachFile)
            {
                ApplicationArea = All;
                Caption = 'Remove Attachment';
                ToolTip = 'Remove the current attachment.';
                Image = Delete;
                Promoted = true;
                PromotedCategory = Process;
                Enabled = HasAttachment;

                trigger OnAction()
                begin
                    DetachFileFromEmail();
                end;
            }

            action(SendEmail)
            {
                ApplicationArea = All;
                Caption = 'Send Email';
                ToolTip = 'Send the mass email to selected contacts via BCC.';
                Image = SendMail;
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;

                trigger OnAction()
                begin
                    SendMassEmail();
                end;
            }
        }
    }

    var
        SalespersonCode: Code[20];
        FromEmail: Text[80];
        EmailSubject: Text[250];
        EmailBody: Text;
        AttachmentFileName: Text;
        SelectedContactsInfo: Text;
        HasAttachment: Boolean;
        AttachmentTempBlob: Codeunit "Temp Blob";
        AttachmentMimeType: Text;

    trigger OnOpenPage()
    var
        MassEmailMgt: Codeunit "Mass Email Management";
    begin
        FromEmail := MassEmailMgt.GetUserEmail();
        UpdateContactCount();
    end;

    local procedure UpdateContactCount()
    var
        MassEmailMgt: Codeunit "Mass Email Management";
        SelectedCount: Integer;
        TotalCount: Integer;
    begin
        SelectedCount := MassEmailMgt.GetSelectedContactCount(Rec);

        Rec.Reset();
        TotalCount := Rec.Count();

        SelectedContactsInfo := StrSubstNo('%1 of %2 contacts selected', SelectedCount, TotalCount);
    end;

    local procedure SelectAllContacts(SelectValue: Boolean)
    begin
        Rec.Reset();
        if Rec.FindSet(true) then
            repeat
                Rec.Selected := SelectValue;
                Rec.Modify();
            until Rec.Next() = 0;

        UpdateContactCount();
        CurrPage.Update(false);
    end;

    local procedure AttachFileToEmail()
    var
        FileName: Text;
        InStream: InStream;
        OutStream: OutStream;
    begin
        if UploadIntoStream('Select file to attach', '', '', FileName, InStream) then begin
            AttachmentTempBlob.CreateOutStream(OutStream);
            CopyStream(OutStream, InStream);
            AttachmentFileName := FileName;
            AttachmentMimeType := GetMimeType(FileName);
            HasAttachment := true;
        end;
    end;

    local procedure DetachFileFromEmail()
    begin
        Clear(AttachmentTempBlob);
        AttachmentFileName := '';
        AttachmentMimeType := '';
        HasAttachment := false;
    end;

    local procedure GetMimeType(FileName: Text): Text
    var
        FileExtension: Text;
    begin
        FileExtension := LowerCase(CopyStr(FileName, StrPos(FileName, '.') + 1));

        case FileExtension of
            'pdf':
                exit('application/pdf');
            'doc':
                exit('application/msword');
            'docx':
                exit('application/vnd.openxmlformats-officedocument.wordprocessingml.document');
            'xls':
                exit('application/vnd.ms-excel');
            'xlsx':
                exit('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            'txt':
                exit('text/plain');
            'jpg', 'jpeg':
                exit('image/jpeg');
            'png':
                exit('image/png');
            'gif':
                exit('image/gif');
            else
                exit('application/octet-stream');
        end;
    end;

    local procedure SendMassEmail()
    var
        MassEmailMgt: Codeunit "Mass Email Management";
        AttachmentInStream: InStream;
    begin
        if SalespersonCode = '' then begin
            Message('Please select a salesperson first.');
            exit;
        end;

        if EmailSubject = '' then begin
            Message('Please enter an email subject.');
            exit;
        end;

        if EmailBody = '' then begin
            Message('Please enter an email message.');
            exit;
        end;

        if MassEmailMgt.GetSelectedContactCount(Rec) = 0 then begin
            Message('Please select at least one contact.');
            exit;
        end;

        if HasAttachment then
            AttachmentTempBlob.CreateInStream(AttachmentInStream);

        if MassEmailMgt.SendMassEmail(Rec, EmailSubject, EmailBody, AttachmentInStream, AttachmentFileName, AttachmentMimeType) then
            CurrPage.Close();
    end;
}
